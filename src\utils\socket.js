import { io } from 'socket.io-client'

class SocketService {
  constructor() {
    this.socket = null
    this.isConnected = false
    this.messageHandlers = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
  }

  // 连接到Socket.io服务器
  connect(serverUrl = 'http://localhost:3001') {
    if (this.socket && this.isConnected) {
      console.log('Socket already connected')
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        this.socket = io(serverUrl, {
          transports: ['websocket', 'polling'],
          timeout: 5000,
          reconnection: true,
          reconnectionAttempts: this.maxReconnectAttempts,
          reconnectionDelay: 1000,
        })

        // 连接成功
        this.socket.on('connect', () => {
          console.log('Socket connected:', this.socket.id)
          this.isConnected = true
          this.reconnectAttempts = 0
          resolve()
        })

        // 连接错误
        this.socket.on('connect_error', (error) => {
          console.error('Socket connection error:', error)
          this.isConnected = false
          reject(error)
        })

        // 断开连接
        this.socket.on('disconnect', (reason) => {
          console.log('Socket disconnected:', reason)
          this.isConnected = false
          
          // 如果是服务器主动断开，尝试重连
          if (reason === 'io server disconnect') {
            this.socket.connect()
          }
        })

        // 重连尝试
        this.socket.on('reconnect_attempt', (attemptNumber) => {
          console.log(`Socket reconnect attempt ${attemptNumber}`)
          this.reconnectAttempts = attemptNumber
        })

        // 重连成功
        this.socket.on('reconnect', (attemptNumber) => {
          console.log(`Socket reconnected after ${attemptNumber} attempts`)
          this.isConnected = true
          this.reconnectAttempts = 0
        })

        // 重连失败
        this.socket.on('reconnect_failed', () => {
          console.error('Socket reconnection failed')
          this.isConnected = false
        })

        // 监听消息事件
        this.setupMessageHandlers()

      } catch (error) {
        console.error('Failed to create socket connection:', error)
        reject(error)
      }
    })
  }

  // 设置消息处理器
  setupMessageHandlers() {
    if (!this.socket) return

    // 接收新消息
    this.socket.on('message', (data) => {
      console.log('Received message:', data)
      this.handleMessage('message', data)
    })

    // 接收用户加入通知
    this.socket.on('user_joined', (data) => {
      console.log('User joined:', data)
      this.handleMessage('user_joined', data)
    })

    // 接收用户离开通知
    this.socket.on('user_left', (data) => {
      console.log('User left:', data)
      this.handleMessage('user_left', data)
    })

    // 接收在线用户列表
    this.socket.on('online_users', (data) => {
      console.log('Online users:', data)
      this.handleMessage('online_users', data)
    })

    // 接收消息状态更新
    this.socket.on('message_status', (data) => {
      console.log('Message status:', data)
      this.handleMessage('message_status', data)
    })
  }

  // 处理接收到的消息
  handleMessage(type, data) {
    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in message handler for ${type}:`, error)
        }
      })
    }
  }

  // 注册消息处理器
  on(eventType, handler) {
    if (!this.messageHandlers.has(eventType)) {
      this.messageHandlers.set(eventType, new Set())
    }
    this.messageHandlers.get(eventType).add(handler)
  }

  // 移除消息处理器
  off(eventType, handler) {
    const handlers = this.messageHandlers.get(eventType)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.messageHandlers.delete(eventType)
      }
    }
  }

  // 发送消息
  sendMessage(messageData) {
    if (!this.isConnected || !this.socket) {
      console.error('Socket not connected')
      return false
    }

    try {
      this.socket.emit('message', {
        ...messageData,
        timestamp: new Date().toISOString(),
        id: Date.now() + Math.random()
      })
      return true
    } catch (error) {
      console.error('Failed to send message:', error)
      return false
    }
  }

  // 加入聊天室
  joinRoom(roomId, userInfo) {
    if (!this.isConnected || !this.socket) {
      console.error('Socket not connected')
      return false
    }

    try {
      this.socket.emit('join_room', {
        roomId,
        userInfo,
        timestamp: new Date().toISOString()
      })
      return true
    } catch (error) {
      console.error('Failed to join room:', error)
      return false
    }
  }

  // 离开聊天室
  leaveRoom(roomId) {
    if (!this.isConnected || !this.socket) {
      console.error('Socket not connected')
      return false
    }

    try {
      this.socket.emit('leave_room', {
        roomId,
        timestamp: new Date().toISOString()
      })
      return true
    } catch (error) {
      console.error('Failed to leave room:', error)
      return false
    }
  }

  // 发送打字状态
  sendTypingStatus(isTyping, roomId) {
    if (!this.isConnected || !this.socket) {
      return false
    }

    try {
      this.socket.emit('typing', {
        isTyping,
        roomId,
        timestamp: new Date().toISOString()
      })
      return true
    } catch (error) {
      console.error('Failed to send typing status:', error)
      return false
    }
  }

  // 断开连接
  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
      this.isConnected = false
      this.messageHandlers.clear()
      console.log('Socket disconnected manually')
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      socketId: this.socket?.id || null,
      reconnectAttempts: this.reconnectAttempts
    }
  }
}

// 创建单例实例
const socketService = new SocketService()

export default socketService
