{"name": "security-chat-server", "version": "1.0.0", "description": "Socket.io chat server for security system", "main": "chat-server.js", "scripts": {"start": "node chat-server.js", "dev": "nodemon chat-server.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.8.1", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["socket.io", "chat", "real-time", "security"], "author": "Security System", "license": "MIT"}