const express = require('express')
const http = require('http')
const socketIo = require('socket.io')
const cors = require('cors')

const app = express()
const server = http.createServer(app)

// 配置CORS
app.use(cors({
  origin: ["http://localhost:5173", "http://localhost:3000"],
  credentials: true
}))

const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:5173", "http://localhost:3000"],
    methods: ["GET", "POST"],
    credentials: true
  }
})

// 存储在线用户和房间信息
const onlineUsers = new Map()
const rooms = new Map()

// Socket.io连接处理
io.on('connection', (socket) => {
  console.log(`用户连接: ${socket.id}`)

  // 用户加入房间
  socket.on('join_room', (data) => {
    const { roomId, userInfo } = data
    
    // 加入房间
    socket.join(roomId)
    
    // 存储用户信息
    onlineUsers.set(socket.id, {
      ...userInfo,
      socketId: socket.id,
      roomId: roomId,
      joinTime: new Date()
    })
    
    // 更新房间用户列表
    if (!rooms.has(roomId)) {
      rooms.set(roomId, new Set())
    }
    rooms.get(roomId).add(socket.id)
    
    console.log(`用户 ${userInfo.name} 加入房间 ${roomId}`)
    
    // 通知房间内其他用户
    socket.to(roomId).emit('user_joined', {
      user: userInfo,
      message: `${userInfo.name} 加入了聊天`,
      timestamp: new Date().toISOString()
    })
    
    // 发送当前在线用户列表
    const roomUsers = Array.from(rooms.get(roomId) || [])
      .map(socketId => onlineUsers.get(socketId))
      .filter(user => user)
    
    io.to(roomId).emit('online_users', {
      users: roomUsers,
      count: roomUsers.length
    })
  })

  // 处理消息
  socket.on('message', (data) => {
    const user = onlineUsers.get(socket.id)
    if (!user) {
      console.log('未找到用户信息')
      return
    }

    const messageData = {
      id: data.id || Date.now() + Math.random(),
      type: data.type || 'text',
      content: data.content,
      sender: data.sender || user,
      roomId: data.roomId || user.roomId,
      timestamp: data.timestamp || new Date().toISOString()
    }

    console.log(`收到消息 from ${user.name}: ${data.content}`)

    // 广播消息到房间内其他用户（不包括发送者）
    socket.to(messageData.roomId).emit('message', messageData)

    // 发送消息状态确认给发送者
    socket.emit('message_status', {
      messageId: messageData.id,
      status: 'sent',
      timestamp: new Date().toISOString()
    })
  })

  // 处理打字状态
  socket.on('typing', (data) => {
    const user = onlineUsers.get(socket.id)
    if (!user) return

    socket.to(data.roomId).emit('typing', {
      user: user,
      isTyping: data.isTyping,
      timestamp: data.timestamp
    })
  })

  // 用户离开房间
  socket.on('leave_room', (data) => {
    const user = onlineUsers.get(socket.id)
    if (!user) return

    const { roomId } = data
    socket.leave(roomId)

    // 从房间用户列表中移除
    if (rooms.has(roomId)) {
      rooms.get(roomId).delete(socket.id)
      if (rooms.get(roomId).size === 0) {
        rooms.delete(roomId)
      }
    }

    console.log(`用户 ${user.name} 离开房间 ${roomId}`)

    // 通知房间内其他用户
    socket.to(roomId).emit('user_left', {
      user: user,
      message: `${user.name} 离开了聊天`,
      timestamp: new Date().toISOString()
    })

    // 更新在线用户列表
    const roomUsers = Array.from(rooms.get(roomId) || [])
      .map(socketId => onlineUsers.get(socketId))
      .filter(user => user)
    
    io.to(roomId).emit('online_users', {
      users: roomUsers,
      count: roomUsers.length
    })
  })

  // 用户断开连接
  socket.on('disconnect', (reason) => {
    const user = onlineUsers.get(socket.id)
    if (user) {
      console.log(`用户断开连接: ${user.name} (${reason})`)
      
      // 从所有房间中移除用户
      const roomId = user.roomId
      if (roomId && rooms.has(roomId)) {
        rooms.get(roomId).delete(socket.id)
        if (rooms.get(roomId).size === 0) {
          rooms.delete(roomId)
        }
        
        // 通知房间内其他用户
        socket.to(roomId).emit('user_left', {
          user: user,
          message: `${user.name} 离开了聊天`,
          timestamp: new Date().toISOString()
        })
        
        // 更新在线用户列表
        const roomUsers = Array.from(rooms.get(roomId) || [])
          .map(socketId => onlineUsers.get(socketId))
          .filter(u => u)
        
        io.to(roomId).emit('online_users', {
          users: roomUsers,
          count: roomUsers.length
        })
      }
      
      // 从在线用户列表中移除
      onlineUsers.delete(socket.id)
    }
  })
})

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    onlineUsers: onlineUsers.size,
    activeRooms: rooms.size
  })
})

// 获取统计信息
app.get('/stats', (req, res) => {
  const stats = {
    onlineUsers: onlineUsers.size,
    activeRooms: rooms.size,
    rooms: Array.from(rooms.entries()).map(([roomId, users]) => ({
      roomId,
      userCount: users.size
    }))
  }
  res.json(stats)
})

const PORT = process.env.PORT || 3001

server.listen(PORT, () => {
  console.log(`聊天服务器运行在端口 ${PORT}`)
  console.log(`健康检查: http://localhost:${PORT}/health`)
  console.log(`统计信息: http://localhost:${PORT}/stats`)
})

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...')
  server.close(() => {
    console.log('服务器已关闭')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...')
  server.close(() => {
    console.log('服务器已关闭')
    process.exit(0)
  })
})
