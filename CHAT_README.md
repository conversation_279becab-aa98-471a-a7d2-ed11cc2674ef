# 实时通讯功能使用说明

## 功能概述

本项目已成功集成了仿微信风格的实时通讯功能，支持文本消息、图片消息、语音消息和表情符号的发送与接收。

## 主要特性

### 🎨 界面设计
- **仿微信风格**：采用微信聊天界面的设计风格，用户体验友好
- **响应式布局**：支持桌面端和移动端的完美适配
- **消息气泡**：发送方和接收方消息气泡有明显的颜色区分
- **头像显示**：支持用户头像和联系人头像的显示

### 💬 消息功能
- **文本消息**：支持多行文本输入和发送
- **图片消息**：支持图片上传和预览功能
- **语音消息**：提供语音录制和播放功能（UI已实现，需要后端支持）
- **表情符号**：内置丰富的表情符号选择器

### 🔄 实时通讯
- **Socket.io集成**：使用Socket.io实现实时双向通讯
- **消息状态**：支持消息发送状态的实时反馈
- **在线状态**：显示用户在线状态和房间信息
- **自动重连**：网络断开时自动尝试重连

## 文件结构

```
src/
├── components/
│   └── Chat.vue              # 主聊天组件
├── utils/
│   └── socket.js             # Socket.io服务封装
└── router/
    └── router.js             # 路由配置

server/
├── chat-server.js            # Socket.io服务器
└── package.json              # 服务器依赖配置
```

## 使用方法

### 1. 启动聊天服务器

```bash
# 进入服务器目录
cd server

# 安装依赖
npm install

# 启动服务器
npm start
# 或者使用开发模式（自动重启）
npm run dev
```

服务器将在 `http://localhost:3001` 启动。

### 2. 启动前端应用

```bash
# 在项目根目录
npm run dev
```

前端应用将在 `http://localhost:5173` 启动。

### 3. 访问聊天功能

1. 登录系统后，在首页点击"实时通讯"按钮
2. 或者直接访问 `/chat` 路由
3. 开始发送和接收消息

## 功能详解

### 消息类型

#### 文本消息
- 在输入框中输入文本
- 按Enter键或点击发送按钮发送
- 支持多行文本（Shift+Enter换行）

#### 图片消息
- 点击图片按钮选择图片文件
- 支持常见图片格式（jpg, png, gif等）
- 图片会自动适应气泡大小

#### 语音消息
- 点击语音按钮切换到语音模式
- 按住"按住说话"按钮录制语音
- 松开按钮结束录制并发送

#### 表情符号
- 点击表情按钮打开表情选择器
- 点击表情符号插入到输入框
- 支持48个常用表情符号

### 实时通讯特性

#### 消息同步
- 所有消息通过Socket.io实时同步
- 支持多用户同时在线聊天
- 消息按时间顺序显示

#### 用户状态
- 显示用户加入/离开通知
- 实时更新在线用户列表
- 支持用户头像和昵称显示

#### 连接管理
- 自动处理网络断开和重连
- 显示连接状态
- 错误处理和用户提示

## 自定义配置

### 修改服务器地址

在 `src/utils/socket.js` 中修改服务器地址：

```javascript
connect(serverUrl = 'http://your-server:port')
```

### 修改用户信息

在 `src/components/Chat.vue` 中修改用户信息：

```javascript
const contactName = ref('你的联系人名称')
const userName = ref('你的用户名')
```

### 修改头像

替换 `src/assets/images/` 目录下的头像文件：
- `default-avatar.svg` - 默认联系人头像
- `user-avatar.svg` - 当前用户头像

## 扩展功能

### 添加新消息类型

1. 在 `Chat.vue` 中添加新的消息类型处理
2. 在 `socket.js` 中添加对应的发送方法
3. 在服务器端添加消息类型处理逻辑

### 集成文件传输

1. 扩展图片上传功能支持更多文件类型
2. 添加文件上传进度显示
3. 实现文件下载功能

### 添加消息历史

1. 集成数据库存储消息历史
2. 实现消息分页加载
3. 添加消息搜索功能

## 注意事项

1. **服务器依赖**：确保Socket.io服务器正常运行
2. **网络环境**：在生产环境中需要配置正确的服务器地址
3. **浏览器兼容性**：建议使用现代浏览器以获得最佳体验
4. **移动端适配**：在移动设备上测试所有功能
5. **安全考虑**：在生产环境中添加适当的身份验证和消息过滤

## 故障排除

### 连接失败
- 检查服务器是否正常启动
- 确认服务器地址和端口配置正确
- 检查防火墙设置

### 消息发送失败
- 检查网络连接状态
- 查看浏览器控制台错误信息
- 确认Socket.io连接正常

### 样式问题
- 清除浏览器缓存
- 检查CSS文件是否正确加载
- 确认响应式样式在目标设备上正常工作

## 技术支持

如有问题，请检查：
1. 浏览器控制台的错误信息
2. 服务器日志输出
3. 网络连接状态
4. 项目依赖是否正确安装

---

**开发完成时间**：2025年7月2日  
**技术栈**：Vue 3 + Socket.io + Ant Design Vue  
**兼容性**：现代浏览器，移动端友好
